CD-HIT is available from Docker now. To build cd-hit docker image within this directory, which has the Dockerfile, use this command:
docker build --tag cd-hit .


Or to build cdhit Docker image without downloading cd-hit, use this command:
docker build --tag cd-hit https://raw.githubusercontent.com/weizhongli/cdhit/master/Docker/Dockerfile


Use this command to find the images
docker image ls


To run a cd-hit program using docker, use a command like this: 
docker run -v `pwd`:/data -w /data cd-hit cd-hit -i input.fa -o output -c 0.9 -n 5 -d 0 

Here, you have an input file named input.fa in your current directory
    option -v `pwd`:`pwd` is to mount your current directory into the docker container at /data
    option -w /data       is to set working dirtory to /data within the docker ocntainer
    cd-hit                (the first cd-hit) is the name of the docker image
    cd-hit                (the second cd-hit) is the cd-hit program you are running
    other options following the second cd-hit are options for cd-hit program


Or use this command to start an interactive shell to run cd-hit programs, from a directory with the input file:
docker run -v `pwd`:/data -w /data -it cd-hit bash

After this command, you will be in the cd-hit Docker container, and your working directory will be at 
/data. From here, you can run cd-hit commands
