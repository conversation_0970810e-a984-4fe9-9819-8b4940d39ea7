For cd-hit

Requirements
Since 4.8.1, cd-hit supports .gz format input file. This requires zlib library. zlib should
be install in most Linux systems, so cd-hit should be compiled without issue. If your system
don't have zlib, please install it first. 
    * On Ubuntu, to install zlib:
        sudo apt install zlib1g-dev
    * On CentOS, to install zlib:
        sudo yum install zlib-devel 


How to compile
  1. Compile with multi-threading support (default): make
  2. Compile without multi-threading support (if you are on very old systems): make openmp=no
  3. Compile without zlib (if you can not install zlib): make zlib=no

Having problems to compile
Please contact the author


For cd-hit-auxtools
  cd cd-hit-auxtools
  make


Compile cd-hit on MacOS
To install CD-HIT on MacOS, first install gcc on your system.
To use Homebrew (https://brew.sh/), see https://formulae.brew.sh/formula/gcc@6. 
Then locate the path to your g++ executable, (e.g. /usr/local/Cellar/gcc/6.3.0_1/bin/g++-6, 
note: yours g++ path is likely to be different), then use command like this:
  make CC=/usr/local/Cellar/gcc/6.3.0_1/bin/g++-6


For psi-cd-hit
  please download BLAST+ (not legacy BLAST) and install the executables in your $PATH

For more information, please visit http://cd-hit.org

Most up-to-date documents are available at https://github.com/weizhongli/cdhit/wiki

cd-hit is also available as web server, visit http://cd-hit.org for web server address.
